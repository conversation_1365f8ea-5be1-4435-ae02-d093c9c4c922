﻿@inherits LayoutComponentBase

<FluentLayout>
    <FluentHeader>
        <div style="display:flex; justify-content: space-between;width:100%">
            <div>
                ECN
            </div>
            <div>
                <AuthorizeView>
                    <Authorized>
                        @context.User.Identity.Name
                    </Authorized>
                </AuthorizeView>
            </div>
        </div>
    </FluentHeader>
    <FluentStack Class="main" Orientation="Orientation.Horizontal" Width="100%">
        <NavMenu />
        <FluentBodyContent Class="body-content">
            <div class="content">
                @Body
            </div>
        </FluentBodyContent>
    </FluentStack>
    <FluentFooter>
        <a href="https://www.fluentui-blazor.net" target="_blank">Documentation and demos</a>
        <FluentSpacer />
        <a href="https://learn.microsoft.com/en-us/aspnet/core/blazor" target="_blank">About Blazor</a>
    </FluentFooter>
</FluentLayout>

<div id="blazor-error-ui" data-nosnippet>
    An unhandled error has occurred.
    <a href="." class="reload">Reload</a>
    <span class="dismiss">🗙</span>
</div>
